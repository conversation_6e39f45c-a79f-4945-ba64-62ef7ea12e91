package modals.customerApp.iosNative.iosNativeHomePage;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeHomeScreen extends BaseIosScreen {

    public IosNativeHomeScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeOther[@name='homeScreen_address']")
    WebElement deliveryAddressDetecting;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='homeScreen_locationName']")
    WebElement deliveryAddressDetected;

    @FindBy(xpath = "//XCUIElementTypeImage[@name='MoreUnSelectedState']")
    WebElement moreScreenBtn;

    @FindBy(xpath = "//XCUIElementTypeImage[@name='HomeUnSelectedState']")
    WebElement homeScreenBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='homeScreen_delieryType']")
    WebElement instant;
    
    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Order placed successfully']")
    WebElement placeOrderMiniTracking;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Preparing your order']")
    WebElement preparingYourOrderMiniTracking;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Ready for delivery']")
    WebElement readyForDeliveryMiniTracking;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Order is on the way']")
    WebElement orderIsOnTheWayMiniTracking;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Order Delivered']")
    WebElement orderDeliveredMiniTracking;
    
    @FindBy(xpath = "//XCUIElementTypeButton[@name='ChevronUp']")
    WebElement expandMiniTracking;
    
    @FindBy(xpath = "//XCUIElementTypeButton[@name='View order details']")
    WebElement viewOrderDetails;

    String scrollableContentContainerSelector = "//XCUIElementTypeScrollView";

    @FindBy(name = "chatBtn")
    WebElement chatBtn;

    @FindBy(name = "homeScreen_categories_title")
    WebElement categoriesTitle;

    @FindBy(xpath = "//XCUIElementTypeButton[@name=\"Home\"]")
    WebElement homeScreenTab;

    String topPicksTitle = "topPicks_title";

    String categoryNameSelector = "cateogry_%s_name";

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='title' and @value='The spotlight']")
    WebElement collectionsTitle;

    @FindBy(xpath = "//XCUIElementTypeImage[contains(@name, 'collection_')]")
    WebElement firstAvailableCollection;

    @FindBy(xpath = "//XCUIElementTypeButton[@name=\"product_6008138c46dc1d0c6c755a6e_increaseBtn\"]")
    WebElement increaseBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[@name=\"product_60002e7a46dc1d0c6c572bc3_decreaseBtn\"]")
    WebElement decreaseBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[@name=\"Cart\"]")
    WebElement cartBtn;

    public boolean isPageDisplayed(){
        return (isElementDisplayed(deliveryAddressDetected) && isElementDisplayed(chatBtn))
                || isElementDisplayed(deliveryAddressDetecting);
    }

    public void pressHomeBtn(){
        wait.until(ExpectedConditions.visibilityOf(homeScreenBtn))
                .click();
    }

    public boolean isCategoriesSectionDisplayed(){
        return isElementDisplayed(categoriesTitle);
    }

    public void pressMoreBtn(){
        wait.until(ExpectedConditions.visibilityOf(moreScreenBtn))
                .click();
    }

    public boolean instantDisplay(){
        return isElementDisplayed(instant);
    }

    public boolean placeOrderMiniTrackingIsDisplayed(){
        return isElementDisplayed(placeOrderMiniTracking);
    }

    public boolean preparingYourOrderMiniTrackingIsDisplayed(){
        return isElementDisplayed(preparingYourOrderMiniTracking);
    }

    public boolean readyForDeliveryMiniTrackingIsDisplayed(){
        return isElementDisplayed(readyForDeliveryMiniTracking);
    }

    public boolean orderIsOnTheWayMiniTrackingIsDisplayed(){
        return isElementDisplayed(orderIsOnTheWayMiniTracking);
    }

    public boolean orderDeliveredMiniTrackingIsDisplayed(){
        return isElementDisplayed(orderDeliveredMiniTracking);
    }

    public void pressExpandMiniTracking(){
        wait.until(ExpectedConditions.elementToBeClickable(expandMiniTracking))
                .click();
    }

    public void pressViewOrderDetails(){
        wait.until(ExpectedConditions.elementToBeClickable(viewOrderDetails))
                .click();
    }

    public WebElement getScrollableContentContainer(){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(scrollableContentContainerSelector)));
    }

    public String getCategoryNameSelector (String categoryId){
        return String.format(categoryNameSelector, categoryId);
    }

    public WebElement getCategoryNameElement(String categoryId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.name(getCategoryNameSelector(categoryId))));
    }

    public boolean isCategoryDisplayed(String categoryId){
        return isElementDisplayed(getCategoryNameElement(categoryId));
    }

    public void pressCategoryById(String categoryId){
        getCategoryNameElement(categoryId)
                .click();
    }

    public boolean isChatBtnDisplayed(){
        return isElementDisplayed(chatBtn);
    }

    public String getTopPicksTitleNameSelector(){
        return topPicksTitle;
    }

    public WebElement getTopPicksTitleUiElement(){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.name(getTopPicksTitleNameSelector())));
    }

    public boolean isTopPicksDisplayed(){
        return isElementDisplayed(getTopPicksTitleUiElement());
    }

    public boolean isHomeScreenDisplayed () {
        return wait.until(ExpectedConditions.visibilityOf(homeScreenTab)).isDisplayed();
    }

    public boolean isCollectionsAvailable(){
        return isElementDisplayed(collectionsTitle);
    }

    public void pressFirstAvailableCollection(){
        wait.until(ExpectedConditions.visibilityOf(firstAvailableCollection)).click();
    }
}

