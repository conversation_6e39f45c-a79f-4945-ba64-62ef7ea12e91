package modals.customerApp.androidNative;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativePromosScreen extends BaseAndroidScreen {

    public AndroidNativePromosScreen(AndroidDriver androidDriver)
    {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }
    @FindBy(xpath = "//android.widget.TextView[contains(@text,'View more')]")
    WebElement viewMoreBtn;
    @FindBy(xpath = "//android.view.View[@content-desc=\"breadfast_topAppBar\"]/android.widget.Button")
    WebElement mfCloseBtn;

    @FindBy(xpath = "//android.widget.TextView[@text= 'Promo codes']")
    WebElement myOffersBtmSheetTitle;
    @FindBy(xpath = "(//android.widget.TextView)[3]")
    WebElement myOffersFirstCouponTitle;
    @FindBy(xpath = "(//android.widget.TextView)[4]")
    WebElement myOffersFirstPromoCouponBody;

    @FindBy(xpath = "(//android.widget.TextView)[7]")
    WebElement myOffersFirstPromoCouponCopyBtn;

    @FindBy(xpath = "(//android.widget.TextView)[6]")
    WebElement myOffersFirstPromoCouponExpiryDate;

    public void pressViewBtn()
    {
        wait.until(ExpectedConditions.visibilityOf(viewMoreBtn)).click();
    }
    public void pressPromosCloseBtn()
    {
        wait.until(ExpectedConditions.visibilityOf(mfCloseBtn)).click();
    }

    public boolean isMyOffersBottomSheetDisplayed ()
    {
        return isElementDisplayed(myOffersBtmSheetTitle);
    }
    public boolean isMyOffersFirstCouponTitleDisplayed ()
    {
        return isElementDisplayed(myOffersFirstCouponTitle);
    }
    public boolean isMyOffersFirstPromoCouponBodyDisplayed ()
    {
        return isElementDisplayed(myOffersFirstPromoCouponBody);
    }
    public void pressFirstPromoCouponCopyBtn()
    {
        wait.until(ExpectedConditions.visibilityOf(myOffersFirstPromoCouponCopyBtn)).click();
    }

    public boolean isMyOffersFirstPromoCouponExpiryDateIsDisplayed ()
    {
        return isElementDisplayed(myOffersFirstPromoCouponExpiryDate);
    }
}
