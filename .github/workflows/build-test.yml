name: Build and Test

on:
  push:
  pull_request:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Select the target environment'
        required: true
        default: 'integration'
        type: choice
        options:
          - testing
          - integration
          - production
      test_country_code:
        description: 'Select the test country code'
        required: true
        default: 'EG'
        type: choice
        options:
          - EG
          - KSA

jobs:
  branch-name-check:
    runs-on: ubuntu-latest
    env:
      BRANCH_NAME: ${{ github.head_ref || github.ref_name }}
    steps:
      - name: Check Branch Naming Convention
        run: |
          branch_name=$BRANCH_NAME
          current_year=$(date +'%Y')
          branch_pattern="^$current_year/sprintQ[0-9]+\.[0-9]+(/[-a-z0-9BPHPO]+)*$"
          echo "Current branch name is: $branch_name"
          echo "Expected pattern: $branch_pattern"
          if [ "$branch_name" == "main" ]; then
            echo "Branch name check passed for main branch."
          elif [[ ! $branch_name =~ $branch_pattern ]]; then
            echo "Error: Branch name '$branch_name' does not match the required naming convention."
            echo "Branch names should start with '$current_year/sprintQ<number>.<number>/' and contain only lowercase letters, numbers, Capital letter 'B', Capital letters 'PHPO' and hyphens."
            exit 1
          else
            echo "Branch name check passed."
          fi

  code-style-validation:
    runs-on: ubuntu-latest
    needs: branch-name-check
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 24
        uses: actions/setup-java@v4
        with:
          java-version: '24'
          distribution: 'oracle'
          cache: maven

      - name: Set up Maven
        uses: stCarolas/setup-maven@v5
        with:
          maven-version: 3.9.8

      - name: Run Checkstyle
        run: |
          set +e  # Prevent script from stopping immediately on error
          mvn checkstyle:check
          CHECKSTYLE_EXIT_CODE=$?
          set -e  # Re-enable stopping the script on error
          if [ $CHECKSTYLE_EXIT_CODE -ne 0 ]; then
            echo "There are errors in the code style checks."
            echo "Please run 'mvn checkstyle:check' on your local machine to see the details of the errors."
            exit $CHECKSTYLE_EXIT_CODE
          fi

  build-and-test:
    name: General Test - ${{ github.event.inputs.environment || 'integration' }}
    runs-on: ubuntu-latest
    needs: code-style-validation
    environment: ${{ github.event.inputs.environment || 'integration' }}
    env:
      SELECTED_ENV: ${{ github.event.inputs.environment || 'integration' }}
      TEST_COUNTRY_CODE: ${{ github.event.inputs.test_country_code || 'EG' }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Runtime (composite)
        uses: ./.github/actions/setup-machine
        with:
          java-version: "24"
          java-distribution: "oracle"
          maven-version: "3.9.9"
          node-version: "22.18.0"

      - name: Configs Setup (composite)
        uses: ./.github/actions/configs-setup
        with:
          target-environment: ${{ env.SELECTED_ENV }}
          test-country-code: ${{ env.TEST_COUNTRY_CODE }}
        env:
          GHA_VARS_JSON: ${{ toJson(vars) }}
          GHA_SECRETS_JSON: ${{ toJson(secrets) }}

      - name: Build with Maven and Execute Tests
        run: mvn test -q -Pdefault-tests -Dmaven.test.failure.ignore=true -Denv=${{ env.SELECTED_ENV }}

      - name: Publish test artifacts (composite)
        if: ${{ always() }}
        uses: ./.github/actions/generate-reports
        with:
          working-directory: .
          allure_artifact_name: allure-reports
          allure_path: target/site/allure-maven-plugin
          upload_screenshots: "true"
          screenshots_artifact_name: screenshots
          screenshots_path: resources/screenshots/**
          screenshots_if_no_files_found: ignore
          screenshots_retention_days: "14"
          testng_artifact_name: testng-reports
          testng_path: target/surefire-reports

      - name: Check Test Results
        if: ${{ always() }}
        id: check_test_results
        run: |
          echo "status=$(grep -E 'Tests run: [0-9]+, Failures: [1-9]+' target/surefire-reports/*.txt | wc -l)" >> $GITHUB_OUTPUT
