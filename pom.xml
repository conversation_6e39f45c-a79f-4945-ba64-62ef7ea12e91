<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.breadfast</groupId>
    <artifactId>QA_Framework</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>24</maven.compiler.source>
        <maven.compiler.target>24</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <allure.version>2.29.1</allure.version>
        <aspectj.version>1.9.24</aspectj.version>
        <surefire.version>3.5.3</surefire.version>
        <selenium.version>4.34.0</selenium.version>
        <appium.version>9.5.0</appium.version>
        <checkstyle.version>3.6.0</checkstyle.version>
        <allure-maven.version>2.15.2</allure-maven.version>
        <allure-report.version>2.30.0</allure-report.version>
        <rest-assured.version>5.5.5</rest-assured.version>
        <testng.version>7.11.0</testng.version>
        <jackson.version>2.19.1</jackson.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.qameta.allure</groupId>
                <artifactId>allure-bom</artifactId>
                <version>${allure.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${surefire.version}</version>
                <configuration>
                    <argLine>
                        -Dfile.encoding=${project.build.sourceEncoding}
                        -javaagent:"${settings.localRepository}/org/aspectj/aspectjweaver/${aspectj.version}/aspectjweaver-${aspectj.version}.jar"
                    </argLine>
                    <suiteXmlFiles>
                        <suiteXmlFile>testng.xml</suiteXmlFile>
                        <suiteXmlFile>sanityng.xml</suiteXmlFile>
                        <suiteXmlFile>stressng.xml</suiteXmlFile>
                        <suiteXmlFile>fintechng.xml</suiteXmlFile>
                        <suiteXmlFile>supplychainng.xml</suiteXmlFile>
                        <suiteXmlFile>fleetappng.xml</suiteXmlFile>
                        <suiteXmlFile>referralsng.xml</suiteXmlFile>
                        <suiteXmlFile>mobileng.xml</suiteXmlFile>
                    </suiteXmlFiles>
                    <testFailureIgnore>false</testFailureIgnore>
                    <reportsDirectory>${project.build.directory}/surefire-reports</reportsDirectory>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.aspectj</groupId>
                        <artifactId>aspectjweaver</artifactId>
                        <version>${aspectj.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>io.qameta.allure</groupId>
                <artifactId>allure-maven</artifactId>
                <version>${allure-maven.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <reportVersion>${allure-report.version}</reportVersion>
                    <resultsDirectory>${project.basedir}/allure-results</resultsDirectory>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>${checkstyle.version}</version>
                <configuration>
                    <!-- Path to your Checkstyle configuration file -->
                    <configLocation>checkstyle.xml</configLocation>
                    <suppressionsLocation>checkstyle-suppressions.xml</suppressionsLocation>
                    <includeTestSourceDirectory>true</includeTestSourceDirectory>
                    <consoleOutput>true</consoleOutput>
                    <failsOnError>true</failsOnError> <!-- Optional: fail the build on a violation -->
                </configuration>
                <executions>
                    <execution>
                        <id>check-style</id>
                        <goals>
                            <goal>check</goal> <!-- Binds checkstyle goal to check phase -->
                        </goals>
                        <phase>validate</phase> <!-- Optional: phase in which to run -->
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>full-tests</id>
            <properties>
                <groups>smoke</groups>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>${surefire.version}</version>
                        <configuration>
                            <suiteXmlFiles>
                                <suiteXmlFile>masterng.xml</suiteXmlFile>
                            </suiteXmlFiles>
                            <systemPropertyVariables>
                                <groups>${groups}</groups>
                            </systemPropertyVariables>
                            <testFailureIgnore>false</testFailureIgnore>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>default-tests</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>${surefire.version}</version>
                        <configuration>
                            <suiteXmlFiles>
                                <suiteXmlFile>testng.xml</suiteXmlFile>
                            </suiteXmlFiles>
                            <testFailureIgnore>false</testFailureIgnore>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>sanity-tests</id>
            <properties>
                <groups></groups>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>${surefire.version}</version>
                        <configuration>
                            <suiteXmlFiles>
                                <suiteXmlFile>${project.basedir}/sanityng.xml</suiteXmlFile>
                            </suiteXmlFiles>
                            <systemPropertyVariables>
                                <groups>${groups}</groups>
                            </systemPropertyVariables>
                            <testFailureIgnore>false</testFailureIgnore>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>mobile-tests</id>
            <properties>
                <testnames>CustomerAppNative_Android</testnames>
                <groups></groups>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>${surefire.version}</version>
                        <configuration>
                            <suiteXmlFiles>
                                <suiteXmlFile>mobileng.xml</suiteXmlFile>
                            </suiteXmlFiles>
                            <systemPropertyVariables>
                                <testnames>${testnames}</testnames>
                                <groups>${groups}</groups>
                            </systemPropertyVariables>
                            <properties>
                                <property>
                                    <name>testnames</name>
                                    <value>${testnames}</value>
                                </property>
                            </properties>
                            <testFailureIgnore>false</testFailureIgnore>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>stress-tests</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>${surefire.version}</version>
                        <configuration>
                            <suiteXmlFiles>
                                <suiteXmlFile>${project.basedir}/stressng.xml</suiteXmlFile>
                            </suiteXmlFiles>
                            <testFailureIgnore>false</testFailureIgnore>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>fintech-tests</id>
            <properties>
                <groups></groups>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>${surefire.version}</version>
                        <configuration>
                            <suiteXmlFiles>
                                <suiteXmlFile>${project.basedir}/fintechng.xml</suiteXmlFile>
                            </suiteXmlFiles>
                            <systemPropertyVariables>
                                <groups>${groups}</groups>
                            </systemPropertyVariables>
                            <testFailureIgnore>false</testFailureIgnore>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>supplychain-tests</id>
            <properties>
                <testnames>PlanningCenterTests</testnames>
                <groups></groups>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>${surefire.version}</version>
                        <configuration>
                            <suiteXmlFiles>
                                <suiteXmlFile>${project.basedir}/supplychainng.xml</suiteXmlFile>
                            </suiteXmlFiles>
                            <systemPropertyVariables>
                                <testnames>${testnames}</testnames>
                                <groups>${groups}</groups>
                            </systemPropertyVariables>
                            <properties>
                                <property>
                                    <name>testnames</name>
                                    <value>${testnames}</value>
                                </property>
                            </properties>
                            <testFailureIgnore>false</testFailureIgnore>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>fleetApp-tests</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>${surefire.version}</version>
                        <configuration>
                            <suiteXmlFiles>
                                <suiteXmlFile>${project.basedir}/fleetappng.xml</suiteXmlFile>
                            </suiteXmlFiles>
                            <testFailureIgnore>false</testFailureIgnore>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>referrals-tests</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>${surefire.version}</version>
                        <configuration>
                            <suiteXmlFiles>
                                <suiteXmlFile>${project.basedir}/referralsng.xml</suiteXmlFile>
                            </suiteXmlFiles>
                            <testFailureIgnore>false</testFailureIgnore>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <dependencies>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-server</artifactId>
            <version>12.0.5</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-servlet</artifactId>
            <version>11.0.19</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty.websocket</groupId>
            <artifactId>websocket-server</artifactId>
            <version>9.4.53.v20231009</version>
        </dependency>
        <dependency>
            <groupId>javax.websocket</groupId>
            <artifactId>javax.websocket-api</artifactId>
            <version>1.1</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.5.6</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>3.4.4</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>log4j-over-slf4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-to-slf4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>jul-to-slf4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.alexdlaird</groupId>
            <artifactId>java-ngrok</artifactId>
            <version>2.3.8</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.12.0</version>
        </dependency>
        <dependency>
            <groupId>io.rest-assured</groupId>
            <artifactId>rest-assured</artifactId>
            <version>${rest-assured.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-transport</artifactId>
            <version>4.1.106.Final</version>
        </dependency>
        <dependency>
            <groupId>org.seleniumhq.selenium</groupId>
            <artifactId>selenium-java</artifactId>
            <version>${selenium.version}</version>
        </dependency>
        <dependency>
            <groupId>org.seleniumhq.selenium</groupId>
            <artifactId>selenium-chrome-driver</artifactId>
            <version>${selenium.version}</version>
        </dependency>
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <version>${testng.version}</version>
        </dependency>
        <dependency>
            <groupId>org.seleniumhq.selenium</groupId>
            <artifactId>selenium-support</artifactId>
            <version>${selenium.version}</version>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20250107</version>
        </dependency>
        <dependency>
            <groupId>net.datafaker</groupId>
            <artifactId>datafaker</artifactId>
            <version>2.4.3</version>
        </dependency>
        <dependency>
            <groupId>io.qameta.allure</groupId>
            <artifactId>allure-testng</artifactId>
            <version>${allure.version}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/io.rest-assured/json-schema-validator -->
        <dependency>
            <groupId>io.rest-assured</groupId>
            <artifactId>json-schema-validator</artifactId>
            <version>5.5.1</version>
        </dependency>
        <dependency>
            <groupId>io.qameta.allure</groupId>
            <artifactId>allure-java-commons</artifactId>
            <version>${allure.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.3.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>ru.yandex.qatools.allure</groupId>
            <artifactId>allure-report-builder</artifactId>
            <version>2.3</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.jcraft</groupId>
                    <artifactId>jsch</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>jcl-over-slf4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.appium</groupId>
            <artifactId>java-client</artifactId>
            <version>${appium.version}</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.libphonenumber</groupId>
            <artifactId>libphonenumber</artifactId>
            <version>9.0.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.14.0</version>
        </dependency>
        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>5.9</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
            <version>1.80</version>
        </dependency>
        <dependency>
            <groupId>com.github.mwiede</groupId>
            <artifactId>jsch</artifactId>
            <version>0.2.25</version>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>9.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
            <version>5.1</version>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>7.4</version> </dependency>
    </dependencies>

</project>
